import React, { createContext, useContext, useState, useEffect } from 'react';
import { getSetting } from '../constants/Storage';
import { ThemeMode } from '../constants/Theme';
import { getLocales } from 'expo-localization';

interface SettingsContextType {
    currency: string;
    language: string;
    hideBudgetModule: boolean;
    hideMemberSection: boolean;
    hideExcludeFromBudget: boolean;
    hideShoppingPlatformSection: boolean;
    themeMode: ThemeMode;
    refreshSettings: () => void;
}

// 获取基于设备locale的默认值
const getLocaleBasedDefaults = () => {
    const locales = getLocales();
    const languageCode = locales[0]?.languageTag?.split('-')[0] || 'en';
    const countryCode = locales[0]?.regionCode || 'US';

    // 根据语言和地区设置默认货币
    let defaultCurrency = '$'; // 默认美元
    if (languageCode === 'zh') {
        if (countryCode === 'CN' || countryCode === 'TW' || countryCode === 'HK' || countryCode === 'MO') {
            defaultCurrency = '¥';
        }
    } else if (countryCode === 'GB') {
        defaultCurrency = '£';
    } else if (countryCode === 'JP') {
        defaultCurrency = '¥';
    } else if (['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'FI', 'IE', 'PT', 'GR', 'LU', 'MT', 'CY', 'SK', 'SI', 'EE', 'LV', 'LT'].includes(countryCode)) {
        defaultCurrency = '€';
    }

    return {
        currency: defaultCurrency,
        language: languageCode === 'zh' ? 'zh' : 'en'
    };
};

const defaults = getLocaleBasedDefaults();

const SettingsContext = createContext<SettingsContextType>({
    currency: defaults.currency,
    language: defaults.language,
    hideBudgetModule: false,
    hideMemberSection: false,
    hideExcludeFromBudget: false,
    hideShoppingPlatformSection: false,
    themeMode: 'light',
    refreshSettings: () => { },
});

export const SettingsProvider = ({ children }: { children: React.ReactNode }) => {
    const [currency, setCurrency] = useState(defaults.currency);
    const [language, setLanguage] = useState(defaults.language);
    const [hideBudgetModule, setHideBudgetModule] = useState(false);
    const [hideMemberSection, setHideMemberSection] = useState(false);
    const [hideExcludeFromBudget, setHideExcludeFromBudget] = useState(false);
    const [hideShoppingPlatformSection, setHideShoppingPlatformSection] = useState(false);
    const [themeMode, setThemeMode] = useState<ThemeMode>('light');
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    useEffect(() => {
        const loadSettings = async () => {
            const savedCurrency = await getSetting('currency');
            if (savedCurrency) {
                setCurrency(savedCurrency);
            }

            const savedLanguage = await getSetting('language');
            if (savedLanguage) {
                setLanguage(savedLanguage);
            }

            const savedHideBudgetModule = await getSetting('hideBudgetModule');
            if (savedHideBudgetModule) {
                setHideBudgetModule(savedHideBudgetModule === 'true');
            }

            const savedHideMemberSection = await getSetting('hideMemberSection');
            if (savedHideMemberSection) {
                setHideMemberSection(savedHideMemberSection === 'true');
            }

            const savedHideExcludeFromBudget = await getSetting('hideExcludeFromBudget');
            if (savedHideExcludeFromBudget) {
                setHideExcludeFromBudget(savedHideExcludeFromBudget === 'true');
            }

            const savedHideShoppingPlatformSection = await getSetting('hideShoppingPlatformSection');
            if (savedHideShoppingPlatformSection) {
                setHideShoppingPlatformSection(savedHideShoppingPlatformSection === 'true');
            }

            const savedThemeMode = await getSetting('themeMode');
            if (savedThemeMode && (savedThemeMode === 'light' || savedThemeMode === 'dark')) {
                setThemeMode(savedThemeMode as ThemeMode);
            }
        };

        loadSettings();
    }, [refreshTrigger]);

    const refreshSettings = () => {
        setRefreshTrigger(prev => prev + 1);
    };

    return (
        <SettingsContext.Provider value={{ currency, language, hideBudgetModule, hideMemberSection, hideExcludeFromBudget, hideShoppingPlatformSection, themeMode, refreshSettings }}>
            {children}
        </SettingsContext.Provider>
    );
};

export const useSettings = () => useContext(SettingsContext); 